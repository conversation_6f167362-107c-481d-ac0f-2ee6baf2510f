import { api } from "../api"; // Assuming api is exported like this
import { saveVideoToLibrary } from "../youtubeService";

// Mock the api module
jest.mock("../api", () => ({
  api: {
    post: jest.fn(),
  },
}));

describe("youtubeService", () => {
  beforeEach(() => {
    // Clear mock calls before each test
    (api.post as jest.Mock).mockClear();
  });

  it("saveVideoToLibrary should call api.post with correct URL and empty object payload", async () => {
    const videoId = "TEST_VIDEO_ID";
    const expectedUrl = `/youtube/save-to-library/${videoId}`;

    // Mock the post call to return a successful response
    (api.post as jest.Mock).mockResolvedValue({ data: { message: "Success" } });

    // Call the function
    await saveVideoToLibrary(videoId);

    // Assert that api.post was called correctly
    expect(api.post).toHaveBeenCalledTimes(1);
    expect(api.post).toHaveBeenCalledWith(expectedUrl, {});
  });

  it("saveVideoToLibrary should propagate errors from api.post", async () => {
    const videoId = "ERROR_VIDEO_ID";
    const expectedError = new Error("Network Error");

    // Mock the post call to reject with an error
    (api.post as jest.Mock).mockRejectedValue(expectedError);

    // Call the function and expect it to throw
    await expect(saveVideoToLibrary(videoId)).rejects.toThrow("Network Error");

    // Assert that api.post was called
    expect(api.post).toHaveBeenCalledTimes(1);
    expect(api.post).toHaveBeenCalledWith(
      `/youtube/save-to-library/${videoId}`,
      {}
    );
  });
});
