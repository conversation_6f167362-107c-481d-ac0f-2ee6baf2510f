"""
FastAPI main application for Writer v2.
This replaces the Flask application with a modern async FastAPI implementation.
"""

import os
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware

# Import settings from config module
from app.config import get_settings

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan context manager.
    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    logger.info("Starting FastAPI application...")
    
    try:
        # Initialize Supabase repositories
        from app.utils.supabase_hooks import init_supabase_repositories_on_startup
        init_supabase_repositories_on_startup()
        
        logger.info("FastAPI application startup completed successfully")

    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        # Continue with basic setup
        logger.warning("Continuing with basic application setup")
        pass
    
    yield
    
    # Shutdown
    logger.info("Shutting down FastAPI application...")
    
    try:
        # Cleanup Supabase repositories
        from app.utils.supabase_hooks import cleanup_supabase_repositories_on_shutdown
        cleanup_supabase_repositories_on_shutdown()
        
    except Exception as e:
        logger.error(f"Error during shutdown cleanup: {e}")
    
    logger.info("FastAPI application shutdown completed")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    # Configure logging
    settings = get_settings()
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create FastAPI app with lifespan
    app = FastAPI(
        title="Writer v2 API",
        description="AI-powered content creation and analysis platform",
        version="2.0.0",
        lifespan=lifespan
    )
    
    # Configure CORS
    cors_origins = settings.cors_allowed_origins.split(",")
    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*", "x-time-offset"],  # Include custom headers
    )
    
    # Add request logging middleware
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        logger.debug(f"Request: {request.method} {request.url.path}")
        response = await call_next(request)
        return response
    
    # Include FastAPI routers
    from app.routers import (
        health, 
        activity, 
        business, 
        chat, 
        auth, 
        content, 
        youtube, 
        feedback, 
        ai_assistant, 
        wizard, 
        debug
    )

    # Include all routers with appropriate prefixes and tags
    app.include_router(health.router)
    app.include_router(health.health_router)  # Add the /health alias router
    app.include_router(auth.router)
    app.include_router(activity.router)
    app.include_router(business.router)
    app.include_router(content.router)
    app.include_router(youtube.router)
    app.include_router(feedback.router)
    app.include_router(ai_assistant.router)
    app.include_router(wizard.router)
    app.include_router(debug.router)
    app.include_router(chat.router)
    
    logger.info("FastAPI application created successfully")
    return app


# Create the application instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=5000,
        reload=True,
        log_level="info"
    )
