"""
Health check routes for FastAPI.
These replace the basic health endpoints in main.py.
"""

from fastapi import APIRouter
from typing import Dict, Any

router = APIRouter(prefix="/api", tags=["health"])

# Create a separate router for the root health endpoint without prefix
health_router = APIRouter(tags=["health"])


@router.get("/")
async def root() -> Dict[str, str]:
    """Root endpoint returning welcome message."""
    return {"message": "Welcome to Writer v2 API (FastAPI)"}


@router.get("/health")
async def health_check() -> Dict[str, str]:
    """Health check endpoint at /api/health."""
    return {"status": "healthy", "framework": "FastAPI"}


@health_router.get("/health")
async def health_check_alias() -> Dict[str, str]:
    """Health check endpoint at /health (alias for compatibility)."""
    return {"status": "healthy", "framework": "FastAPI"}


@router.get("/health/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check with more information."""
    return {
        "status": "healthy",
        "framework": "FastAPI",
        "version": "2.0.0",
        "migration_status": "Phase 2 - Route Migration in Progress"
    }
