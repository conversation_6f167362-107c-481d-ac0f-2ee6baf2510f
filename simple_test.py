import requests

try:
    print("Testing /health endpoint...")
    response = requests.get("http://localhost:5000/health", timeout=5)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {e}")

try:
    print("\nTesting /api/health endpoint...")
    response = requests.get("http://localhost:5000/api/health", timeout=5)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {e}")

try:
    print("\nTesting /api/api/health endpoint...")
    response = requests.get("http://localhost:5000/api/api/health", timeout=5)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {e}")
