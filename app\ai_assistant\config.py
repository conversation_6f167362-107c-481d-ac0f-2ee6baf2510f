# app/ai_assistant/config.py
"""
Configuration module for AI assistant settings - FastAPI version.

This module centralizes all configuration related to AI models,
providers, and settings to make it easier to switch between
different models or providers. Migrated from Flask to use FastAPI
dependency injection for settings.

It supports purpose-specific model selection with environment variable overrides.
"""

import logging
import warnings
from typing import Dict, Any, Optional

# Configure logging for this module
logger = logging.getLogger(__name__)

# Purpose-specific AI model configuration
# Maps a purpose string to a model identifier string.
# Serves as fallback if purpose-specific or default environment variables are not set.
AI_MODEL_CONFIG: Dict[str, str] = {
    "default": "deepseek/deepseek-chat-v3-0324:free",
    "general_chat": "deepseek/deepseek-chat-v3-0324:free",
    "chat_stream": "deepseek/deepseek-chat-v3-0324:free",  # Added for streaming chat
    "writing_assistant": "deepseek/deepseek-chat-v3-0324:free",  # Placeholder, map to default
    "transcription": "deepseek/deepseek-chat-v3-0324:free",  # Placeholder, map to default
    "business_optimization": "deepseek/deepseek-chat-v3-0324:free",  # Use a reliable model for business profile optimization
    "audience_analysis": "deepseek/deepseek-chat-v3-0324:free",  # Use a reliable model for audience analysis
    "youtube_content": "deepseek/deepseek-chat-v3-0324:free",  # Added for YouTube content
    "transcript_formatting": "deepseek/deepseek-chat-v3-0324:free",  # Added for transcript formatting
    # Add other purposes and their preferred models here
}


def _get_settings():
    """Get settings from FastAPI dependency injection."""
    from app.dependencies import get_settings
    return get_settings()


def get_model_for_purpose(purpose: str = "default") -> str:
    """
    Get the appropriate AI model identifier for a given purpose.

    Resolution Order:
    1. Purpose-specific environment variable (e.g., AI_GENERAL_CHAT_MODEL).
    2. If purpose is "default":
        a. AI_DEFAULT_MODEL environment variable.
        b. AI_MODEL_CONFIG["default"].
    3. If purpose is not "default":
        a. Look up purpose in AI_MODEL_CONFIG.
        b. If not found, log a warning and fall back to the default model
           by recursively calling get_model_for_purpose("default").

    Args:
        purpose (str): The intended purpose for the AI model (e.g., "general_chat", "writing_assistant").
                       Defaults to "default".

    Returns:
        str: The model identifier string (e.g., "deepseek/deepseek-chat-v3-0324:free").
    """
    logger.debug(f"Attempting to get model for purpose: '{purpose}'")
    
    settings = _get_settings()

    # 1. Check purpose-specific environment variable override
    purpose_specific_key = f"ai_{purpose.lower().replace('-', '_')}_model"
    purpose_model_override = getattr(settings, purpose_specific_key, None)
    logger.debug(
        f"Checking settings attr: '{purpose_specific_key}', value: '{purpose_model_override}'"
    )
    if purpose_model_override and purpose_model_override.strip():
        logger.debug(
            f"Using model from purpose-specific setting '{purpose_specific_key}': {purpose_model_override}"
        )
        return purpose_model_override

    # 2. Handle "default" purpose specifically
    if purpose == "default":
        # 2a. Check default environment variable override
        default_model_override = getattr(settings, 'ai_default_model', None)
        logger.debug(
            f"Checking settings attr: 'ai_default_model', value: '{default_model_override}'"
        )
        if default_model_override and default_model_override.strip():
            logger.debug(
                f"Using model from default setting 'ai_default_model': {default_model_override}"
            )
            return default_model_override
        # 2b. Fallback to hardcoded default in config dict
        default_model = AI_MODEL_CONFIG.get("default")
        logger.debug(f"Using model from AI_MODEL_CONFIG['default']: {default_model}")
        if not default_model:  # Should ideally not happen if config is valid
            logger.error("Critical: 'default' key missing in AI_MODEL_CONFIG!")
            # Provide a failsafe default model string if config is broken
            return "deepseek/deepseek-chat-v3-0324:free"  # Failsafe
        return default_model

    # 3. Handle non-default purposes
    else:
        # 3a. Look up purpose in the config dictionary
        config_model = AI_MODEL_CONFIG.get(purpose)
        logger.debug(
            f"Checking AI_MODEL_CONFIG for purpose '{purpose}', found: '{config_model}'"
        )
        if config_model:
            logger.debug(
                f"Using model from AI_MODEL_CONFIG['{purpose}']: {config_model}"
            )
            return config_model
        else:
            # 3b. Purpose not found, log warning and fall back to default
            logger.warning(
                f"Purpose '{purpose}' not found in AI_MODEL_CONFIG. Falling back to default model."
            )
            # Recursively call to get the resolved default model (handles env vars for default too)
            return get_model_for_purpose("default")


def get_default_model() -> str:
    """
    DEPRECATED: Get the default AI model name. Use get_model_for_purpose('default') instead.

    Returns:
        str: The default model name.
    """
    warnings.warn(
        "get_default_model() is deprecated and will be removed in a future version. "
        "Use get_model_for_purpose('default') instead.",
        DeprecationWarning,
        stacklevel=2,
    )
    logger.warning(
        "Deprecated function get_default_model() called. Redirecting to get_model_for_purpose('default')."
    )
    return get_model_for_purpose("default")


def get_model_config() -> Dict[str, Any]:
    """
    Get the base model configuration dictionary.
    Note: This returns the *new* AI_MODEL_CONFIG structure for reference,
    but specific values should be retrieved using dedicated functions
    that check settings and the new purpose logic.

    Returns:
        Dict[str, Any]: The new model configuration structure.
    """
    logger.warning(
        "get_model_config() called. Returning the new AI_MODEL_CONFIG structure. Its direct use might be limited."
    )
    return AI_MODEL_CONFIG.copy()


def get_api_provider() -> Optional[str]:
    """
    Get the current API provider name from settings.

    Defaults to 'openrouter' if `ai_provider` is not set in settings.

    Returns:
        Optional[str]: The API provider name (e.g., "openai", "openrouter"), or None if not configured properly.
    """
    settings = _get_settings()
    provider = getattr(settings, 'ai_provider', None) or "openrouter"
    logger.debug(
        f"API Provider check: settings 'ai_provider' value: '{getattr(settings, 'ai_provider', None)}', Resolved to: '{provider}'"
    )
    if provider:
        return provider.lower()
    logger.warning("API Provider could not be determined.")
    return None


def get_api_url() -> str:
    """
    Get the API URL based on the provider configuration.

    Returns:
        str: The API URL for the configured provider.
    """
    provider = get_api_provider()
    
    if provider == "openai":
        return "https://api.openai.com/v1"
    elif provider == "openrouter":
        return "https://openrouter.ai/api/v1"
    else:
        # Default fallback
        logger.warning(f"Unknown provider '{provider}', using OpenRouter URL as fallback")
        return "https://openrouter.ai/api/v1"


def get_max_tokens(content_type: str = "default") -> int:
    """
    Get the maximum tokens configuration for a given content type.

    Args:
        content_type (str): The content type (e.g., "default", "chat", "chat_stream").

    Returns:
        int: The maximum number of tokens.
    """
    # Default token limits
    token_limits = {
        "default": 4096,
        "chat": 4096,
        "chat_stream": 4096,
        "writing_assistant": 8192,
        "business_optimization": 8192,
        "audience_analysis": 8192,
    }
    
    return token_limits.get(content_type, 4096)


def get_model_params(custom_params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Get model parameters configuration.

    Args:
        custom_params (Dict[str, Any], optional): Custom parameters to override defaults.

    Returns:
        Dict[str, Any]: The model parameters dictionary.
    """
    # Default parameters
    default_params = {
        "temperature": 0.7,
        "top_p": 1.0,
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0,
    }
    
    if custom_params:
        default_params.update(custom_params)
    
    return default_params


def get_api_key() -> Optional[str]:
    """
    Get the API key for the current provider.

    Returns:
        Optional[str]: The API key, or None if not configured.
    """
    settings = _get_settings()
    provider = get_api_provider()
    
    if provider == "openai":
        return getattr(settings, 'openai_api_key', None)
    elif provider == "openrouter":
        return getattr(settings, 'openrouter_api_key', None)
    else:
        logger.warning(f"Unknown provider '{provider}', cannot determine API key")
        return None
