# Use Node.js LTS base image (updated from 16 to 18)
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./

# Install dependencies with clean cache
RUN npm ci --only=production=false && \
    npm cache clean --force

# Copy source code explicitly to avoid issues with spaces in paths
COPY public/ ./public/
COPY src/ ./src/
COPY tsconfig.json ./
COPY .env* ./

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 && \
    chown -R nextjs:nodejs /app
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Start development server
CMD ["npm", "start"]