[x] Conceptual Summary
    • Core Problem: The AI assistant endpoints return errors / do not generate content, blocking user functionality.

[P] Reproduce the failure
    • Run pytest tests/test_ai_assistant_endpoints.py to observe failing cases.
    • Manually call /api/ai_assistant/test-connection and /api/ai_assistant/generate-content via HTTP client.

[ ] Collect runtime diagnostics
    • Capture stack traces from backend log during failing requests.
    • Enable DEBUG logging for app.ai_assistant modules.

[ ] Trace request flow
    • File: app/routers/ai_assistant.py → generate_content_new
    • File: app/ai_assistant/common.py → get_ai_generator
    • File: app/ai_assistant/openrouter_adapter.py & openai_adapter.py → generate_content

[ ] Identify async-sync mismatches
    • Note that routers call ai_generator.generate_content without await, but adapter methods are async.

[ ] Verify configuration
    • Ensure OPENROUTER_API_KEY / OPENAI_API_KEY and AI_PROVIDER env vars are set.
    • Confirm app.dependencies.get_settings exposes ai_provider correctly.

[ ] Review placeholder logic
    • app/ai_assistant/ai_generation.py contains placeholder generate_content_with_retry; confirm it is unused or replace later.

[ ] Cross-reference library docs using Context7
    • Retrieve OpenAI Python SDK async usage docs.
    • Retrieve httpx async client patterns.

[ ] Formulate hypotheses & prioritise
    • Primary: Missing await causes coroutine not awaited resulting in error response.
    • Secondary: Missing API keys / invalid config.
    • Tertiary: Placeholder implementation not wired.

[ ] Draft remediation proposals (do NOT implement yet)
    • Update router handlers to await async methods or provide sync wrapper.
    • Add robust error handling & retries using ai_generation.generate_content_with_retry.
    • Expand unit tests for async flow.

[ ] Define verification criteria
    • All AI assistant tests pass.
    • /api/ai_assistant/* endpoints return 200 with valid content.
    • Logs show successful connection to AI provider.

[ ] Next Steps
    • After investigation results, iterate on code changes following TDD workflow. 