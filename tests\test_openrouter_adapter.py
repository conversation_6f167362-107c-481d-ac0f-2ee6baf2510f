import pytest
from unittest.mock import patch, MagicMock
from app.ai_assistant.openrouter_adapter import OpenRouterAdapter

# --- Fixtures ---


@pytest.fixture(scope="module")
def app():
    """Create and configure a Flask app for testing"""
    app = create_app(
        {
            "TESTING": True,
            "JWT_SECRET_KEY": "test-secret-key",
            "SECRET_KEY": "test-secret-key",
            "AI_PROVIDER": "openrouter",
            "OPENROUTER_API_KEY": "test-or-key",
            "YOUTUBE_API_KEY": "test-yt-key",
            "SUPABASE_URL": "http://localhost:54321",
            "SUPABASE_KEY": "test-supabase-key",
            "USE_SUPABASE": True,
        }
    )

    with app.app_context():
        pass
    yield app
    with app.app_context():
        pass


@pytest.fixture
def mock_requests_post():
    """Fixture to mock requests.post"""
    with patch("requests.post") as mock_post:
        yield mock_post


@pytest.fixture
def mock_successful_response():
    """Fixture for a successful API response mock"""
    mock_resp = MagicMock()
    mock_resp.status_code = 200
    mock_resp.json.return_value = {
        "choices": [{"message": {"content": "Mock AI Response"}}],
        "usage": {"total_tokens": 100},
        "model": "mock-model",
    }
    mock_resp.raise_for_status.return_value = None
    return mock_resp


@pytest.fixture
def mock_error_response():
    """Fixture for an error API response mock"""
    mock_resp = MagicMock()
    mock_resp.status_code = 500
    mock_resp.json.return_value = {"error": {"message": "API Error"}}
    mock_resp.raise_for_status.side_effect = Exception("API Error")
    return mock_resp


@pytest.fixture
def mock_generate_chat():
    """Fixture to mock the generate_chat_response method specifically."""
    # This mock is intended to be used with `@patch` in the test function
    # e.g., @patch('path.to.Adapter.generate_chat_response')
    # It doesn't need to return anything itself, the patch decorator handles it.
    # However, returning a MagicMock can sometimes be useful for complex scenarios.
    return MagicMock(name="MockGenerateChatResponseMethod")


@pytest.fixture(autouse=True)
def mock_env_vars():
    """Mock environment variables"""
    with patch.dict(
        os.environ,
        {
            "OPENROUTER_API_KEY": "test-or-key",
            "APP_URL": "http://test.app",
            "API_BASE_URL": "http://localhost:5000",  # For handle_youtube_request tests
        },
    ):
        yield


@pytest.fixture
def mock_youtube_service():
    """Fixture to mock YouTubeService"""
    mock_service = MagicMock(spec=YouTubeService)
    # Configure default return values for common methods
    mock_service.extract_video_id.return_value = "dQw4w9WgXcQ"
    mock_service.get_video_details.return_value = {
        "title": "Mock Video Title",
        "view_count": 1000,
    }
    mock_service.get_transcript.return_value = "This is the mock transcript."
    mock_service.analyze_video.return_value = {
        "sentiment": "positive",
        "summary": "Mock analysis summary.",
    }
    mock_service.search_videos.return_value = [
        {"title": "Mock Search Result 1", "video_id": "mock1"}
    ]
    return mock_service


# --- Test Cases ---


def test_adapter_init_success(app):
    """Test successful initialization with API key from env"""
    with app.app_context():
        adapter = OpenRouterAdapter()
        assert adapter.api_key == "test-or-key"
        assert adapter.provider == "openrouter"


def test_adapter_init_no_key(app):
    """Test adapter initialization fails if OPENROUTER_API_KEY is not set."""
    # Explicitly set the env var to None/empty or unset it for this test's scope
    with patch.dict(os.environ, {"OPENROUTER_API_KEY": ""}):
        # Patch the get_api_key function directly within the config module
        with patch(
            "app.ai_assistant.config.get_api_key", return_value=None
        ) as mock_get_key:
            # Ensure we are within an application context for config loading etc.
            with app.app_context():
                with pytest.raises(ValueError, match="OpenRouter API key is required"):
                    # Attempt to initialize the adapter, which should fail
                    OpenRouterAdapter()
                # Optionally, assert that the mocked get_api_key was called
                mock_get_key.assert_called_once()


def test_generate_content_success(app, mock_requests_post, mock_successful_response):
    """Test successful content generation"""
    mock_requests_post.return_value = mock_successful_response
    with app.app_context():
        adapter = OpenRouterAdapter()
        result = adapter.generate_content(prompt="Test prompt")

    assert result["generated_text"] == "Mock AI Response"
    assert (
        result["error"] is None if "error" in result else True
    )  # Check error key absence or False
    assert result["tokens_used"] == 100
    mock_requests_post.assert_called_once()
    call_args = mock_requests_post.call_args[1]
    assert call_args["json"]["messages"][-1]["content"] == "Test prompt"
    assert "Authorization" in call_args["headers"]
    assert call_args["headers"]["Authorization"] == "Bearer test-or-key"


def test_generate_content_api_error(app, mock_requests_post, mock_error_response):
    """Test content generation with API error"""
    mock_requests_post.return_value = mock_error_response
    with app.app_context():
        adapter = OpenRouterAdapter()
        result = adapter.generate_content(prompt="Test prompt")

    assert result["error"] is True
    assert "API Error" in result["error_message"]
    assert "Error generating content" in result["generated_text"]


def test_generate_chat_response_success(
    app, mock_requests_post, mock_successful_response
):
    """Test successful chat response generation"""
    mock_requests_post.return_value = mock_successful_response
    with app.app_context():
        adapter = OpenRouterAdapter()
        messages = [{"role": "user", "content": "Hello"}]
        result = adapter.generate_chat_response(messages=messages)

    assert result["generated_text"] == "Mock AI Response"
    assert result.get("error") is None or result.get("error") is False
    mock_requests_post.assert_called_once()
    call_args = mock_requests_post.call_args[1]
    # System prompt + user message
    assert len(call_args["json"]["messages"]) == 2
    assert call_args["json"]["messages"][1]["content"] == "Hello"


def test_format_transcript_success(app, mock_requests_post, mock_successful_response):
    """Test successful transcript formatting"""
    mock_requests_post.return_value = mock_successful_response
    with app.app_context():
        adapter = OpenRouterAdapter()
        result = adapter.format_transcript("Raw transcript")

    assert result["success"] is True
    assert result["content"] == "Mock AI Response"
    mock_requests_post.assert_called_once()
    call_args = mock_requests_post.call_args[1]
    assert (
        "format this transcript" in call_args["json"]["messages"][1]["content"].lower()
    )


def test_test_connection_success(app, mock_requests_post, mock_successful_response):
    """Test successful connection test"""
    mock_requests_post.return_value = mock_successful_response
    with app.app_context():
        adapter = OpenRouterAdapter()
        result = adapter.test_connection()
    assert result["success"] is True
    assert "Successfully connected" in result["message"]


def test_test_connection_failure(app, mock_requests_post, mock_error_response):
    """Test failed connection test"""
    mock_error_response.status_code = 500  # Ensure it's not 500 for this test
    mock_requests_post.return_value = mock_error_response
    with app.app_context():
        adapter = OpenRouterAdapter()
        result = adapter.test_connection()
    assert result["success"] is False
    assert "API Error" in result["message"]


def test_test_connection_auth_error(app, mock_requests_post, mock_error_response):
    """Test connection test with auth error"""
    mock_error_response.status_code = 401  # Simulate auth error
    mock_error_response.raise_for_status.side_effect = None  # Don't raise for 401
    mock_requests_post.return_value = mock_error_response
    with app.app_context():
        adapter = OpenRouterAdapter()
        result = adapter.test_connection()
    assert result["success"] is False
    assert "Authentication failed" in result["message"]


# --- Tests for handle_youtube_request ---


@patch("app.ai_assistant.openrouter_adapter.OpenRouterAdapter.generate_chat_response")
def test_handle_youtube_request_with_url(
    mock_generate_chat_method, mock_youtube_service, app, mock_successful_response
):
    """Test handle_youtube_request when a URL is provided (using YouTubeService)"""

    # Mock final AI call using the patched method
    mock_generate_chat_method.return_value = {
        "generated_text": "AI response about video",
        "video_data_used": True,
    }

    with app.app_context():
        # Inject the mocked service (assuming adapter is modified to accept it)
        # For now, let's patch the __init__ or instance attribute directly for the test
        with patch.object(
            OpenRouterAdapter,
            "__init__",
            lambda self, api_key=None: setattr(
                self, "youtube_service", mock_youtube_service
            ),
        ) as mock_init:
            adapter = OpenRouterAdapter()
            # Manually set other necessary attributes if __init__ is fully mocked
            adapter.api_key = "test-or-key"
            adapter.api_url = "https://openrouter.ai/api/v1/chat/completions"
            adapter.model = "test-model"

            prompt = "Tell me about https://www.youtube.com/watch?v=dQw4w9WgXcQ anD summarize it."
            messages = [{"role": "user", "content": prompt}]
            # Remove user_token
            adapter.handle_youtube_request(prompt=prompt, messages=messages)

    assert result["generated_text"] == "AI response about video"
    assert result["video_data_used"] is True

    # Verify service methods were called (adjust based on actual logic)
    # _determine_youtube_request_type determines which methods are called.
    # The prompt asks for info and summary, likely triggering 'analysis'
    mock_youtube_service.extract_video_id.assert_called_once_with(
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    )
    # Depending on implementation, it might call details+transcript OR just analyze_video
    # Let's assume it calls analyze_video for a summary request
    mock_youtube_service.analyze_video.assert_called_once_with("dQw4w9WgXcQ")
    mock_youtube_service.get_video_details.assert_called_once_with(
        "dQw4w9WgXcQ"
    )  # Analysis might need details
    mock_youtube_service.get_transcript.assert_not_called()

    # Verify generate_chat_response was called
    mock_generate_chat_method.assert_called_once()
    # Check that the call to generate_chat_response includes formatted video data
    call_args, call_kwargs = mock_generate_chat_method.call_args
    assert "video_data" in call_kwargs
    # Check content of video_data based on mocked analyze_video response
    assert "Mock analysis summary." in str(call_kwargs["video_data"])  # Simple check


@patch("app.ai_assistant.openrouter_adapter.OpenRouterAdapter.generate_chat_response")
def test_handle_youtube_request_no_url(
    mock_generate_chat_method, mock_youtube_service, app
):
    """Test handle_youtube_request when no URL is provided (should fall back)"""
    # Configure the mock generate_chat_response method
    mock_generate_chat_method.return_value = {"generated_text": "Regular AI response"}

    with app.app_context():
        # Inject the mocked service
        with patch.object(
            OpenRouterAdapter,
            "__init__",
            lambda self, api_key=None: setattr(
                self, "youtube_service", mock_youtube_service
            ),
        ) as mock_init:
            adapter = OpenRouterAdapter()
            adapter.api_key = "test-or-key"  # Manually set attributes if needed
            adapter.api_url = "https://openrouter.ai/api/v1/chat/completions"
            adapter.model = "test-model"

            prompt = "General question"
            messages = [{"role": "user", "content": prompt}]
            # Remove user_token
            adapter.handle_youtube_request(prompt=prompt, messages=messages)

    # Expect it to have called the patched generate_chat_response
    assert result["generated_text"] == "Regular AI response"
    mock_generate_chat_method.assert_called_once()
    # Ensure no youtube service methods were called
    mock_youtube_service.extract_video_id.assert_not_called()
    mock_youtube_service.get_video_details.assert_not_called()
    mock_youtube_service.get_transcript.assert_not_called()
    mock_youtube_service.analyze_video.assert_not_called()
    mock_youtube_service.search_videos.assert_not_called()


# --- TODO: Add new tests for specific request types ---


# Example: Test for 'details' request
@patch("app.ai_assistant.openrouter_adapter.OpenRouterAdapter.generate_chat_response")
def test_handle_youtube_request_details(
    mock_generate_chat_method, mock_youtube_service, app
):
    mock_generate_chat_method.return_value = {
        "generated_text": "AI response with details"
    }
    with app.app_context():
        with (
            patch.object(
                adapter,
                "__init__",
                return_value=None,
            ),  # Mock __init__
            patch.object(
                adapter,
                "_init_youtube_service",
                return_value=None,
            ),  # Mock youtube init
            patch.object(
                adapter,
                "_ensure_youtube_service",
                return_value=mock_youtube_service,  # Ensure service is returned
            ),
            patch.object(adapter, "youtube_service", mock_youtube_service),
        ) as _mock_init:
            adapter = OpenRouterAdapter()
            adapter.api_key = "test-or-key"
            adapter.api_url = "https://openrouter.ai/api/v1/chat/completions"
            adapter.model = "test-model"
            prompt = "Get the details for https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Updated prompt
            messages = [{"role": "user", "content": prompt}]
            adapter.handle_youtube_request(prompt=prompt, messages=messages)

    mock_youtube_service.extract_video_id.assert_called_once()
    mock_youtube_service.get_video_details.assert_called_once_with("dQw4w9WgXcQ")
    mock_youtube_service.get_transcript.assert_not_called()
    mock_youtube_service.analyze_video.assert_not_called()
    mock_generate_chat_method.assert_called_once()
    call_args, call_kwargs = mock_generate_chat_method.call_args
    assert "video_data" in call_kwargs
    assert "Mock Video Title" in str(call_kwargs["video_data"])


# Example: Test for 'transcript' request
@patch("app.ai_assistant.openrouter_adapter.OpenRouterAdapter.generate_chat_response")
def test_handle_youtube_request_transcript(
    mock_generate_chat_method, mock_youtube_service, app
):
    mock_generate_chat_method.return_value = {
        "generated_text": "AI response with transcript"
    }
    with app.app_context():
        with (
            patch.object(adapter, "__init__", return_value=None),
            patch.object(adapter, "_init_youtube_service", return_value=None),
            patch.object(
                adapter,
                "_ensure_youtube_service",
                return_value=mock_youtube_service,
            ),
            patch.object(adapter, "youtube_service", mock_youtube_service),
        ) as _mock_init:
            adapter = OpenRouterAdapter()
            adapter.api_key = "test-or-key"
            adapter.api_url = "https://openrouter.ai/api/v1/chat/completions"
            adapter.model = "test-model"
            prompt = (
                "Get the transcript for https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            )
            messages = [{"role": "user", "content": prompt}]
            adapter.handle_youtube_request(prompt=prompt, messages=messages)

    mock_youtube_service.extract_video_id.assert_called_once()
    mock_youtube_service.get_transcript.assert_called_once_with("dQw4w9WgXcQ")
    mock_youtube_service.get_video_details.assert_called_once_with(
        "dQw4w9WgXcQ"
    )  # Transcript request might also fetch details
    mock_youtube_service.analyze_video.assert_not_called()
    mock_generate_chat_method.assert_called_once()
    call_args, call_kwargs = mock_generate_chat_method.call_args
    assert "video_data" in call_kwargs
    assert "mock transcript" in str(call_kwargs["video_data"])


# Example: Test for 'search' request
@patch("app.ai_assistant.openrouter_adapter.OpenRouterAdapter.generate_chat_response")
def test_handle_youtube_request_search(
    mock_generate_chat_method, mock_youtube_service, app
):
    mock_generate_chat_method.return_value = {
        "generated_text": "AI response with search results"
    }
    with app.app_context():
        with (
            patch.object(adapter, "__init__", return_value=None),
            patch.object(adapter, "_init_youtube_service", return_value=None),
            patch.object(
                adapter,
                "_ensure_youtube_service",
                return_value=mock_youtube_service,
            ),
            patch.object(adapter, "youtube_service", mock_youtube_service),
        ) as _mock_init:
            adapter = OpenRouterAdapter()
            adapter.api_key = "test-or-key"
            adapter.api_url = "https://openrouter.ai/api/v1/chat/completions"
            adapter.model = "test-model"
            # Ensure the prompt matches the adapter's search detection logic
            prompt = "search youtube for cool videos"
            messages = [{"role": "user", "content": prompt}]
            adapter.handle_youtube_request(prompt=prompt, messages=messages)

    mock_youtube_service.extract_video_id.assert_not_called()  # No URL extraction for search
    mock_youtube_service.search_videos.assert_called_once_with(
        "cool videos"
    )  # Assuming query extraction
    mock_youtube_service.get_video_details.assert_not_called()
    mock_youtube_service.analyze_video.assert_not_called()
    mock_generate_chat_method.assert_called_once()
    call_args, call_kwargs = mock_generate_chat_method.call_args
    assert "video_data" in call_kwargs  # Should pass search results
    assert "Mock Search Result 1" in str(call_kwargs["video_data"])


# Add more tests for handle_youtube_request covering different request types,
# internal API errors, etc.
